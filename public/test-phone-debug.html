<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobbLogg - Phone Auto-population Debug</title>
    <style>
        body {
            font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1f2937;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        .header h1 {
            color: #dc2626;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            color: #6b7280;
            font-size: 1.1rem;
            margin: 0;
        }
        .debug-banner {
            background: #fffbeb;
            border: 2px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .debug-banner h2 {
            color: #92400e;
            margin: 0 0 10px 0;
            font-size: 1.5rem;
        }
        .debug-banner p {
            color: #a16207;
            margin: 0;
            font-weight: 500;
        }
        .debug-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .debug-section h2 {
            color: #1f2937;
            margin: 0 0 20px 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d1d5db;
        }
        .feature-card h3 {
            color: #2563eb;
            margin: 0 0 10px 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .feature-card p {
            color: #4b5563;
            margin: 0 0 15px 0;
            font-size: 0.95rem;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 5px 0;
            color: #6b7280;
            font-size: 0.9rem;
        }
        .feature-list li:before {
            content: "🔍 ";
            margin-right: 8px;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            margin: 15px 0;
        }
        .test-instructions {
            background: #eff6ff;
            border: 2px solid #bfdbfe;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-instructions h3 {
            color: #1e40af;
            margin: 0 0 15px 0;
            font-size: 1.3rem;
        }
        .test-instructions ol {
            color: #1e3a8a;
            margin: 0;
            padding-left: 20px;
        }
        .test-instructions li {
            margin: 8px 0;
            font-weight: 500;
        }
        .api-info {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .api-info h3 {
            color: #0c4a6e;
            margin: 0 0 15px 0;
            font-size: 1.2rem;
        }
        .api-info p {
            color: #075985;
            margin: 0 0 10px 0;
        }
        .potential-issue {
            background: #fef2f2;
            border: 2px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .potential-issue h3 {
            color: #991b1b;
            margin: 0 0 15px 0;
            font-size: 1.2rem;
        }
        .potential-issue p {
            color: #7f1d1d;
            margin: 0 0 10px 0;
        }
        .solution-box {
            background: #f0fdf4;
            border: 2px solid #bbf7d0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .solution-box h3 {
            color: #166534;
            margin: 0 0 15px 0;
            font-size: 1.2rem;
        }
        .solution-box p {
            color: #15803d;
            margin: 0 0 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐛 Phone Auto-population Debug</h1>
            <p>Investigating phone number auto-population issues in contractor onboarding</p>
        </div>

        <div class="debug-banner">
            <h2>🔍 DEBUG MODE ACTIVE</h2>
            <p>Enhanced logging added to trace phone auto-population flow</p>
        </div>

        <!-- Issue Analysis -->
        <div class="debug-section">
            <h2>📋 Issue Analysis</h2>
            <p>The phone number field is not auto-populating from Brønnøysundregisteret data, while other fields (contact person, email) work correctly.</p>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔍 Investigation Points</h3>
                    <ul class="feature-list">
                        <li>API data extraction from Brønnøysundregisteret</li>
                        <li>Phone number format conversion</li>
                        <li>Auto-population logic in Step3ContactDetails</li>
                        <li>PhoneInput component compatibility</li>
                        <li>State management and useEffect dependencies</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>✅ Working Components</h3>
                    <ul class="feature-list">
                        <li>Contact person auto-population</li>
                        <li>Email auto-population</li>
                        <li>LockedInput display with toggles</li>
                        <li>Manual phone entry and validation</li>
                        <li>PhoneInput component functionality</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- API Investigation -->
        <div class="debug-section">
            <h2>🌐 Brønnøysundregisteret API Investigation</h2>
            
            <div class="api-info">
                <h3>📡 API Endpoint Structure</h3>
                <p><strong>Search API:</strong> https://data.brreg.no/enhetsregisteret/api/enheter</p>
                <p><strong>Single Entity:</strong> https://data.brreg.no/enhetsregisteret/api/enheter/{orgNumber}</p>
                <p><strong>Expected Fields:</strong> enhet.telefon, enhet.epostadresse</p>
            </div>

            <div class="potential-issue">
                <h3>⚠️ Potential Issue: Missing Phone Data</h3>
                <p>The Brønnøysundregisteret API may not consistently provide phone numbers in the 'telefon' field for all companies. This could be because:</p>
                <ul class="feature-list">
                    <li>Phone numbers are not mandatory in the business registry</li>
                    <li>Companies may not have updated their contact information</li>
                    <li>Phone data might be in a different field or format</li>
                    <li>API response structure may have changed</li>
                </ul>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔧 Debug Logging Added</h3>
                    <div class="code-block">
// API extraction logging
console.log('[CompanyLookup] Registry contact data:', {
  telefon: enhet.telefon,
  epostadresse: enhet.epostadresse,
  orgNumber: enhet.organisasjonsnummer
});

// Auto-population logging
console.log('[Step3ContactDetails] Phone auto-population:', {
  registryPhone: brregData.registryContact?.phone,
  extractedPhone: extractedPhone,
  formDataPhone: formData.phone
});
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>📞 Phone Extraction Function</h3>
                    <div class="code-block">
export function extractNorwegianPhone(registryPhone?: string): string {
  if (!registryPhone) return '';
  
  const digits = registryPhone.replace(/\D/g, '');
  
  // +4712345678 -> 12345678
  if (digits.startsWith('47') && digits.length === 10) {
    return digits.substring(2);
  }
  
  // 12345678 -> 12345678
  if (digits.length === 8) {
    return digits;
  }
  
  return '';
}
                    </div>
                </div>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="test-instructions">
            <h3>🧪 Debug Testing Steps</h3>
            <ol>
                <li><strong>Open Browser Console:</strong> Press F12 and go to Console tab</li>
                <li><strong>Start Onboarding:</strong> Begin contractor onboarding process</li>
                <li><strong>Search for Company:</strong> In Step 2, search for a Norwegian company</li>
                <li><strong>Check API Logs:</strong> Look for "[CompanyLookup] Registry contact data" logs</li>
                <li><strong>Select Company:</strong> Choose a company from search results</li>
                <li><strong>Go to Step 3:</strong> Navigate to Contact Details step</li>
                <li><strong>Check Auto-population Logs:</strong> Look for "[Step3ContactDetails] Auto-population check" logs</li>
                <li><strong>Verify Phone Data:</strong> Check if registryContact.phone contains data</li>
                <li><strong>Test Phone Extraction:</strong> Verify extractNorwegianPhone function output</li>
            </ol>
        </div>

        <!-- Potential Solutions -->
        <div class="debug-section">
            <h2>💡 Potential Solutions</h2>
            
            <div class="feature-grid">
                <div class="solution-box">
                    <h3>🔧 Solution 1: API Field Investigation</h3>
                    <p>If the 'telefon' field is consistently empty, we may need to:</p>
                    <ul class="feature-list">
                        <li>Check alternative API endpoints for contact data</li>
                        <li>Look for phone numbers in different response fields</li>
                        <li>Investigate if phone data requires special API parameters</li>
                        <li>Consider using roles API for contact information</li>
                    </ul>
                </div>
                
                <div class="solution-box">
                    <h3>📱 Solution 2: Enhanced Phone Extraction</h3>
                    <p>Improve the phone extraction function to handle more formats:</p>
                    <div class="code-block">
// Enhanced phone extraction
export function extractNorwegianPhone(registryPhone?: string): string {
  if (!registryPhone) return '';
  
  // Handle various Norwegian phone formats
  const digits = registryPhone.replace(/\D/g, '');
  
  // Try different format patterns
  if (digits.startsWith('47') && digits.length === 10) {
    return digits.substring(2);
  }
  if (digits.startsWith('0047') && digits.length === 12) {
    return digits.substring(4);
  }
  if (digits.length === 8 && /^[4-9]/.test(digits)) {
    return digits;
  }
  
  return '';
}
                    </div>
                </div>
                
                <div class="solution-box">
                    <h3>🔄 Solution 3: Fallback Strategy</h3>
                    <p>Implement fallback mechanisms for phone data:</p>
                    <ul class="feature-list">
                        <li>Try multiple API endpoints for contact data</li>
                        <li>Check managing director personal contact info</li>
                        <li>Use company website scraping as last resort</li>
                        <li>Provide clear messaging when no phone data available</li>
                    </ul>
                </div>
                
                <div class="solution-box">
                    <h3>🐛 Solution 4: State Management Fix</h3>
                    <p>Ensure proper state management and dependencies:</p>
                    <div class="code-block">
// Check useEffect dependencies
useEffect(() => {
  // Auto-population logic
}, [
  brregData, 
  formData.phone, 
  fieldOverrides.phone,
  // Ensure all dependencies are included
]);

// Verify state updates
if (extractedPhone) {
  console.log('Setting phone:', extractedPhone);
  updates.phone = extractedPhone;
  newAutoPopulated.phone = true;
}
                    </div>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="debug-section">
            <h2>🎯 Next Steps</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>1️⃣ Immediate Actions</h3>
                    <ul class="feature-list">
                        <li>Test with multiple Norwegian companies</li>
                        <li>Check console logs for API response data</li>
                        <li>Verify if any companies have phone data</li>
                        <li>Test phone extraction function manually</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>2️⃣ If No Phone Data Found</h3>
                    <ul class="feature-list">
                        <li>Research alternative API endpoints</li>
                        <li>Check Brønnøysundregisteret documentation</li>
                        <li>Consider removing phone auto-population</li>
                        <li>Update user expectations accordingly</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>3️⃣ If Phone Data Exists</h3>
                    <ul class="feature-list">
                        <li>Fix extraction function for specific formats</li>
                        <li>Ensure proper state management</li>
                        <li>Test auto-population flow end-to-end</li>
                        <li>Remove debug logging after fix</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>4️⃣ Long-term Improvements</h3>
                    <ul class="feature-list">
                        <li>Implement robust error handling</li>
                        <li>Add user feedback for missing data</li>
                        <li>Consider alternative data sources</li>
                        <li>Enhance phone validation and formatting</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Phone Extraction Test -->
        <div class="debug-section">
            <h2>📱 Phone Extraction Function Test</h2>
            <p>Test the extractNorwegianPhone function with various input formats:</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🧪 Test Cases</h3>
                    <div class="code-block">
// Test various phone formats
const testCases = [
  '+4712345678',    // International format
  '4712345678',     // National format with country code
  '12345678',       // Local 8-digit format
  '+47 123 45 678', // Formatted international
  '47 123 45 678',  // Formatted national
  '123 45 678',     // Formatted local
  '0047 12345678',  // Alternative international
  '',               // Empty string
  null,             // Null value
  '123456789',      // 9 digits (invalid)
  '1234567',        // 7 digits (invalid)
];

// Expected results (8-digit format or empty)
const expectedResults = [
  '12345678',  // +4712345678 -> 12345678
  '12345678',  // 4712345678 -> 12345678
  '12345678',  // 12345678 -> 12345678
  '12345678',  // +47 123 45 678 -> 12345678
  '12345678',  // 47 123 45 678 -> 12345678
  '12345678',  // 123 45 678 -> 12345678
  '12345678',  // 0047 12345678 -> 12345678
  '',          // '' -> ''
  '',          // null -> ''
  '',          // 123456789 -> '' (invalid)
  '',          // 1234567 -> '' (invalid)
];
                    </div>
                </div>

                <div class="feature-card">
                    <h3>🔍 Manual Testing</h3>
                    <p>Open browser console and run:</p>
                    <div class="code-block">
// Copy this to browser console to test
function testExtractNorwegianPhone(input) {
  if (!input) return '';
  const digits = input.replace(/\D/g, '');
  if (digits.startsWith('47') && digits.length === 10) {
    return digits.substring(2);
  }
  if (digits.length === 8) {
    return digits;
  }
  return '';
}

// Test all cases
['+4712345678', '4712345678', '12345678', '+47 123 45 678']
  .forEach(test => {
    console.log(`Input: "${test}" -> Output: "${testExtractNorwegianPhone(test)}"`);
  });
                    </div>
                </div>
            </div>
        </div>

        <!-- Debug Banner -->
        <div class="debug-banner">
            <h2>🔍 DEBUG INVESTIGATION READY</h2>
            <p>Enhanced logging is now active - check browser console during onboarding!</p>
        </div>
    </div>

    <script>
        // Add phone extraction test function to window for easy testing
        window.testExtractNorwegianPhone = function(input) {
            console.log('Testing phone extraction for:', input);

            if (!input) {
                console.log('No input provided, returning empty string');
                return '';
            }

            const digits = input.replace(/\D/g, '');
            console.log('Digits extracted:', digits);

            if (digits.startsWith('47') && digits.length === 10) {
                const result = digits.substring(2);
                console.log('Converted from 10-digit format:', result);
                return result;
            }

            if (digits.length === 8) {
                console.log('Already 8-digit format:', digits);
                return digits;
            }

            console.log('Invalid format, returning empty string. Length:', digits.length);
            return '';
        };

        // Auto-run tests when page loads
        console.log('=== Phone Extraction Function Tests ===');
        const testCases = [
            '+4712345678',
            '4712345678',
            '12345678',
            '+47 123 45 678',
            '47 123 45 678',
            '123 45 678',
            '',
            '123456789',
            '1234567'
        ];

        testCases.forEach(testCase => {
            const result = window.testExtractNorwegianPhone(testCase);
            console.log(`✓ "${testCase}" -> "${result}"`);
        });
        console.log('=== End Tests ===');
    </script>
</body>
</html>
