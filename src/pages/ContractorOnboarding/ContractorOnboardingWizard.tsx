import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation, Routes, Route, Navigate } from 'react-router-dom';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, StepProgress, WizardStep } from '../../components/ui';
import { Step1Introduction } from './steps/Step1Introduction';
import { Step2CompanyLookup } from './steps/Step2CompanyLookup';
import { Step3ContactDetails } from './steps/Step3ContactDetails';
import { Step4Confirmation } from './steps/Step4Confirmation';
import { useContractorOnboardingStore } from './hooks/useContractorOnboardingStore';

const STORAGE_KEY = 'jobblogg-contractor-onboarding';

// Form data interface for the multi-step wizard
export interface ContractorOnboardingFormData {
  // Company data
  companyName: string;
  orgNumber: string;
  contactPerson: string;
  phone: string;
  email: string;
  // Enhanced address fields
  streetAddress: string;
  postalCode: string;
  city: string;
  entrance: string;
  notes: string;

  // Brønnøysundregisteret data tracking
  brregFetchedAt?: number;
  brregData?: any;
  useCustomAddress?: boolean;
  lockedFields?: {
    orgNumber?: boolean;
    address?: boolean;
  };
  companySelected?: boolean;
}

// Default form data
const defaultFormData: ContractorOnboardingFormData = {
  companyName: '',
  orgNumber: '',
  contactPerson: '',
  phone: '',
  email: '',
  streetAddress: '',
  postalCode: '',
  city: '',
  entrance: '',
  notes: '',
  useCustomAddress: false,
  lockedFields: {
    orgNumber: false,
    address: false
  },
  companySelected: false
};

/**
 * ContractorOnboardingWizard component
 * 
 * Multi-step wizard for contractor onboarding with:
 * - Introduction step
 * - Company lookup with Brønnøysundregisteret integration
 * - Contact details
 * - Confirmation and submission
 */
const ContractorOnboardingWizard: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useUser();
  const [showSuccess, setShowSuccess] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [createdCompanyId, setCreatedCompanyId] = useState<string | null>(null);
  const [redirectPath, setRedirectPath] = useState<string | null>(null);

  // Get form data from store
  const { 
    formData, 
    setFormData, 
    currentStep, 
    setCurrentStep,
    brregData,
    setBrregData,
    brregFetchedAt,
    setBrregFetchedAt,
    useCustomAddress,
    setUseCustomAddress,
    lockedFields,
    setLockedFields,
    companySelected,
    setCompanySelected
  } = useContractorOnboardingStore();

  // Convex mutations
  const createContractorCompany = useMutation(api.contractorOnboarding.createContractorCompany);
  const updateOnboardingStatus = useMutation(api.contractorOnboarding.updateContractorOnboardingStatus);

  // Parse redirect path from URL query parameters
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const redirect = params.get('redirect');
    if (redirect) {
      setRedirectPath(redirect);
    }
  }, [location.search]);

  // Define wizard steps
  const steps = [
    { id: 1, title: 'Introduksjon', description: 'Velkommen til JobbLogg', isCompleted: currentStep > 1, isActive: currentStep === 1 },
    { id: 2, title: 'Bedriftsinformasjon', description: 'Registrer din bedrift', isCompleted: currentStep > 2, isActive: currentStep === 2 },
    { id: 3, title: 'Kontaktdetaljer', description: 'Legg til kontaktinformasjon', isCompleted: currentStep > 3, isActive: currentStep === 3 },
    { id: 4, title: 'Bekreftelse', description: 'Gjennomgå og bekreft', isCompleted: currentStep > 4, isActive: currentStep === 4 }
  ];

  // Autosave functionality with debouncing
  const saveToLocalStorage = useCallback(() => {
    const dataToSave = {
      formData,
      currentStep,
      brregData,
      brregFetchedAt,
      useCustomAddress,
      lockedFields,
      companySelected,
      timestamp: Date.now()
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
  }, [formData, currentStep, brregData, brregFetchedAt, useCustomAddress, lockedFields, companySelected]);

  // Load saved data from localStorage
  useEffect(() => {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        
        // Check if data is not too old (7 days)
        const now = Date.now();
        const dataAge = now - (parsedData.timestamp || 0);
        const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
        
        if (dataAge < maxAge) {
          setFormData(parsedData.formData || defaultFormData);
          setCurrentStep(parsedData.currentStep || 1);
          setBrregData(parsedData.brregData || null);
          setBrregFetchedAt(parsedData.brregFetchedAt || null);
          setUseCustomAddress(parsedData.useCustomAddress || false);
          setLockedFields(parsedData.lockedFields || { orgNumber: false, address: false });
          setCompanySelected(parsedData.companySelected || false);
        } else {
          // Data is too old, clear it
          localStorage.removeItem(STORAGE_KEY);
        }
      } catch (error) {
        console.error('Error parsing saved onboarding data:', error);
        localStorage.removeItem(STORAGE_KEY);
      }
    }
  }, []);

  // Save data on form changes (debounced)
  useEffect(() => {
    const hasFormData = Object.values(formData).some(value => 
      typeof value === 'string' && value.trim() !== ''
    );

    if (!hasFormData) return;

    const debounceTimer = setTimeout(() => {
      saveToLocalStorage();
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [formData, saveToLocalStorage]);

  // Save data before unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      saveToLocalStorage();
    };

    const handleUnload = () => {
      saveToLocalStorage();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, [saveToLocalStorage]);

  // Clear localStorage on successful submission
  const clearSavedData = () => {
    localStorage.removeItem(STORAGE_KEY);
  };

  // Update form data
  const updateFormData = (updates: Partial<ContractorOnboardingFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  // Navigation functions
  const goToNextStep = () => {
    if (currentStep < 4) {
      // Save data immediately before navigating to ensure no data loss
      saveToLocalStorage();
      setCurrentStep(currentStep + 1);
      setErrors({});
      
      // Update URL to reflect current step
      navigate(`/contractor-onboarding/step/${currentStep + 1}${redirectPath ? `?redirect=${encodeURIComponent(redirectPath)}` : ''}`);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      saveToLocalStorage();
      setCurrentStep(currentStep - 1);
      setErrors({});
      
      // Update URL to reflect current step
      navigate(`/contractor-onboarding/step/${currentStep - 1}${redirectPath ? `?redirect=${encodeURIComponent(redirectPath)}` : ''}`);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!user) return;
    
    setIsLoading(true);
    setErrors({});
    
    try {
      // Create contractor company
      const companyId = await createContractorCompany({
        clerkUserId: user.id,
        name: formData.companyName,
        contactPerson: formData.contactPerson,
        phone: formData.phone,
        email: formData.email,
        streetAddress: formData.streetAddress,
        postalCode: formData.postalCode,
        city: formData.city,
        entrance: formData.entrance,
        orgNumber: formData.orgNumber,
        notes: formData.notes,
        brregFetchedAt: formData.brregFetchedAt,
        brregData: formData.brregData,
        useCustomAddress: formData.useCustomAddress
      });
      
      setCreatedCompanyId(companyId);
      
      // Update onboarding status
      await updateOnboardingStatus({
        clerkUserId: user.id,
        completed: true
      });
      
      // Clear saved data
      clearSavedData();
      
      // Show success message
      setShowSuccess(true);
      
      // Redirect after a short delay
      setTimeout(() => {
        if (redirectPath) {
          navigate(redirectPath);
        } else {
          navigate('/');
        }
      }, 3000);
    } catch (error: any) {
      console.error('Error creating contractor company:', error);
      setErrors({
        submit: error.message || 'Det oppstod en feil ved registrering av bedriften. Vennligst prøv igjen.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Redirect to the appropriate step based on URL
  useEffect(() => {
    const path = location.pathname;
    const stepMatch = path.match(/\/contractor-onboarding\/step\/(\d+)/);
    
    if (stepMatch) {
      const stepNumber = parseInt(stepMatch[1], 10);
      if (stepNumber >= 1 && stepNumber <= 4 && stepNumber !== currentStep) {
        setCurrentStep(stepNumber);
      }
    } else if (path === '/contractor-onboarding') {
      // Redirect to step 1 if just at the base path
      navigate(`/contractor-onboarding/step/1${redirectPath ? `?redirect=${encodeURIComponent(redirectPath)}` : ''}`);
    }
  }, [location.pathname, currentStep, navigate, redirectPath]);

  return (
    <PageLayout containerWidth="narrow">
      {/* Success Message */}
      {showSuccess && (
        <div className="bg-jobblogg-success-soft rounded-xl p-8 text-center animate-scale-in">
          <div className="w-16 h-16 bg-jobblogg-success rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-jobblogg-text-strong">Registrering fullført!</h2>
            <p className="text-jobblogg-text-muted">Din bedrift er nå registrert i JobbLogg.</p>
            <p className="text-jobblogg-text-muted text-sm">Du blir nå videresendt...</p>
          </div>
        </div>
      )}

      {/* Main Wizard Content */}
      {!showSuccess && (
        <div className="max-w-3xl mx-auto">
          {/* Step Progress */}
          <StepProgress steps={steps} currentStep={currentStep} className="mb-8" />

          {/* Wizard Form */}
          <div className="bg-white rounded-xl shadow-lg border border-jobblogg-border overflow-hidden">
            <div className="p-8">
              <Routes>
                <Route path="/" element={<Navigate to="step/1" replace />} />
                
                <Route path="step/1" element={
                  <Step1Introduction
                    onNext={goToNextStep}
                  />
                } />
                
                <Route path="step/2" element={
                  <Step2CompanyLookup
                    formData={formData}
                    updateFormData={updateFormData}
                    errors={errors}
                    onNext={goToNextStep}
                    onPrevious={goToPreviousStep}
                    brregData={brregData}
                    setBrregData={setBrregData}
                    brregFetchedAt={brregFetchedAt}
                    setBrregFetchedAt={setBrregFetchedAt}
                    useCustomAddress={useCustomAddress}
                    setUseCustomAddress={setUseCustomAddress}
                    lockedFields={lockedFields}
                    setLockedFields={setLockedFields}
                    companySelected={companySelected}
                    setCompanySelected={setCompanySelected}
                    setErrors={setErrors}
                  />
                } />
                
                <Route path="step/3" element={
                  <Step3ContactDetails
                    formData={formData}
                    updateFormData={updateFormData}
                    errors={errors}
                    onNext={goToNextStep}
                    onPrevious={goToPreviousStep}
                    setErrors={setErrors}
                  />
                } />
                
                <Route path="step/4" element={
                  <Step4Confirmation
                    formData={formData}
                    errors={errors}
                    onPrevious={goToPreviousStep}
                    onSubmit={handleSubmit}
                    isLoading={isLoading}
                  />
                } />
                
                <Route path="*" element={<Navigate to="step/1" replace />} />
              </Routes>
            </div>
          </div>
        </div>
      )}
    </PageLayout>
  );
};

export default ContractorOnboardingWizard;
