import React, { useEffect, useState } from 'react';
import { PrimaryButton, SecondaryButton, TextInput, TextArea, FormError, PhoneInput, LockedInput, ToggleSwitch } from '../../../components/ui';
import { extractNorwegianPhone } from '../../../services/companyLookup';
import type { ContractorOnboardingFormData } from '../ContractorOnboardingWizard';

interface Step3ContactDetailsProps {
  formData: ContractorOnboardingFormData;
  updateFormData: (updates: Partial<ContractorOnboardingFormData>) => void;
  errors: { [key: string]: string };
  onNext: () => void;
  onPrevious: () => void;
  setErrors: (errors: { [key: string]: string }) => void;
  brregData: any; // Brønnøysundregisteret data for auto-population
}

/**
 * Step 3: Contact Details
 * 
 * Collects contractor contact information:
 * - Contact person name
 * - Phone number (Norwegian format)
 * - Email address
 * - Optional notes
 */
export const Step3ContactDetails: React.FC<Step3ContactDetailsProps> = ({
  formData,
  updateFormData,
  errors,
  onNext,
  onPrevious,
  setErrors,
  brregData
}) => {
  // State for managing auto-populated fields
  const [autoPopulatedFields, setAutoPopulatedFields] = useState<{
    phone: boolean;
    email: boolean;
  }>({ phone: false, email: false });

  const [fieldOverrides, setFieldOverrides] = useState<{
    phone: boolean;
    email: boolean;
  }>({ phone: false, email: false });

  // Auto-populate fields from Brønnøysundregisteret data
  useEffect(() => {
    if (brregData?.registryContact && !fieldOverrides.phone && !fieldOverrides.email) {
      const updates: Partial<ContractorOnboardingFormData> = {};
      const newAutoPopulated = { ...autoPopulatedFields };

      // Auto-populate phone if available and not already set by user
      if (brregData.registryContact.phone && !formData.phone) {
        const extractedPhone = extractNorwegianPhone(brregData.registryContact.phone);
        if (extractedPhone) {
          updates.phone = extractedPhone;
          newAutoPopulated.phone = true;
        }
      }

      // Auto-populate email if available and not already set by user
      if (brregData.registryContact.email && !formData.email) {
        updates.email = brregData.registryContact.email;
        newAutoPopulated.email = true;
      }

      if (Object.keys(updates).length > 0) {
        updateFormData(updates);
        setAutoPopulatedFields(newAutoPopulated);
      }
    }
  }, [brregData, formData.phone, formData.email, fieldOverrides, autoPopulatedFields, updateFormData]);

  // Handle phone number change (PhoneInput provides raw digits)
  const handlePhoneChange = (digits: string) => {
    updateFormData({ phone: digits });

    // Mark as user-modified if it was auto-populated
    if (autoPopulatedFields.phone) {
      setFieldOverrides(prev => ({ ...prev, phone: true }));
      setAutoPopulatedFields(prev => ({ ...prev, phone: false }));
    }

    // Clear phone error when user types
    if (errors.phone) {
      setErrors({ ...errors, phone: '' });
    }
  };

  // Validate email format as user types
  const handleEmailChange = (value: string) => {
    updateFormData({ email: value });

    // Mark as user-modified if it was auto-populated
    if (autoPopulatedFields.email) {
      setFieldOverrides(prev => ({ ...prev, email: true }));
      setAutoPopulatedFields(prev => ({ ...prev, email: false }));
    }

    // Clear email error when user types
    if (errors.email) {
      setErrors({ ...errors, email: '' });
    }
  };

  // Toggle field override
  const toggleFieldOverride = (field: 'phone' | 'email') => {
    setFieldOverrides(prev => ({ ...prev, [field]: !prev[field] }));
    if (!fieldOverrides[field]) {
      setAutoPopulatedFields(prev => ({ ...prev, [field]: false }));
    }
  };

  // Validate contact person name
  const handleContactPersonChange = (value: string) => {
    updateFormData({ contactPerson: value });
    
    // Clear error when user types
    if (errors.contactPerson) {
      setErrors({ ...errors, contactPerson: '' });
    }
  };

  // Validate form and proceed to next step
  const handleNext = () => {
    const newErrors: { [key: string]: string } = {};

    // Validate contact person
    if (!formData.contactPerson.trim()) {
      newErrors.contactPerson = 'Kontaktperson er påkrevd';
    }

    // Validate phone number (expecting raw digits from PhoneInput)
    if (!formData.phone.trim()) {
      newErrors.phone = 'Telefonnummer er påkrevd';
    } else {
      // Check that we have exactly 8 digits for Norwegian mobile numbers
      const digits = formData.phone.replace(/\D/g, '');
      if (digits.length !== 8) {
        newErrors.phone = 'Telefonnummer må være 8 siffer';
      } else if (!/^[4-9]/.test(digits)) {
        // Norwegian mobile numbers typically start with 4, 5, 9
        newErrors.phone = 'Ugyldig norsk mobilnummer';
      }
    }

    // Validate email
    if (!formData.email.trim()) {
      newErrors.email = 'E-postadresse er påkrevd';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email.trim())) {
        newErrors.email = 'Ugyldig e-postadresse format';
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Clear errors and proceed
    setErrors({});
    onNext();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-jobblogg-text-strong mb-2">
          Kontaktinformasjon
        </h2>
        <p className="text-jobblogg-text-muted">
          Legg til dine kontaktdetaljer for kommunikasjon med kunder
        </p>
      </div>

      {/* Contact Person */}
      <div className="space-y-2">
        <TextInput
          label="Kontaktperson"
          placeholder="Arne Løken"
          required
          fullWidth
          value={formData.contactPerson}
          onChange={(e) => handleContactPersonChange(e.target.value)}
          error={errors.contactPerson}
          helperText="Navn på person som skal være hovedkontakt for prosjekter"
        />
      </div>

      {/* Phone Number */}
      <div className="space-y-2">
        {autoPopulatedFields.phone && !fieldOverrides.phone ? (
          <div className="space-y-2">
            <LockedInput
              label="Telefonnummer"
              value={`+47 ${formData.phone.replace(/(\d{3})(\d{2})(\d{3})/, '$1 $2 $3')}`}
              fullWidth
              helperText="Hentet automatisk fra Brønnøysundregisteret"
            />
            <div className="flex items-center justify-between">
              <span className="text-sm text-jobblogg-text-muted">
                Vil du endre telefonnummer?
              </span>
              <ToggleSwitch
                checked={fieldOverrides.phone}
                onChange={() => toggleFieldOverride('phone')}
                label="Rediger manuelt"
              />
            </div>
          </div>
        ) : (
          <PhoneInput
            label="Telefonnummer"
            required
            fullWidth
            value={formData.phone}
            onChange={handlePhoneChange}
            error={errors.phone}
            helperText={autoPopulatedFields.phone ? "Redigerer manuelt (opprinnelig fra Brønnøysundregisteret)" : "Norsk mobilnummer med +47 landskode"}
          />
        )}
      </div>

      {/* Email Address */}
      <div className="space-y-2">
        {autoPopulatedFields.email && !fieldOverrides.email ? (
          <div className="space-y-2">
            <LockedInput
              label="E-postadresse"
              value={formData.email}
              fullWidth
              helperText="Hentet automatisk fra Brønnøysundregisteret"
            />
            <div className="flex items-center justify-between">
              <span className="text-sm text-jobblogg-text-muted">
                Vil du endre e-postadresse?
              </span>
              <ToggleSwitch
                checked={fieldOverrides.email}
                onChange={() => toggleFieldOverride('email')}
                label="Rediger manuelt"
              />
            </div>
          </div>
        ) : (
          <TextInput
            label="E-postadresse"
            placeholder="<EMAIL>"
            type="email"
            required
            fullWidth
            value={formData.email}
            onChange={(e) => handleEmailChange(e.target.value)}
            error={errors.email}
            helperText={autoPopulatedFields.email ? "Redigerer manuelt (opprinnelig fra Brønnøysundregisteret)" : "E-postadresse for kommunikasjon og varsler"}
          />
        )}
      </div>

      {/* Notes (Optional) */}
      <div className="space-y-2">
        <TextArea
          label="Notater (valgfritt)"
          placeholder="Tilleggsinformasjon om bedriften eller spesielle instruksjoner..."
          fullWidth
          rows={3}
          value={formData.notes}
          onChange={(e) => updateFormData({ notes: e.target.value })}
          error={errors.notes}
          helperText="Eventuell tilleggsinformasjon som kan være nyttig for kunder"
        />
      </div>

      {/* Information Box */}
      <div className="bg-jobblogg-primary-soft rounded-xl p-6">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-jobblogg-text-strong mb-2">
              Hvordan brukes kontaktinformasjonen?
            </h4>
            <div className="space-y-2 text-sm text-jobblogg-text-muted">
              <p>
                • <strong>Kontaktperson:</strong> Vises til kunder i delte prosjekter og chat-meldinger
              </p>
              <p>
                • <strong>Telefon:</strong> Brukes for direkte kontakt og vises i prosjektdetaljer
              </p>
              <p>
                • <strong>E-post:</strong> For varsler, rapporter og kommunikasjon via systemet
              </p>
              <p>
                • <strong>Notater:</strong> Vises kun til kunder i delte prosjekter (valgfritt)
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Form Errors */}
      {errors.submit && (
        <FormError message={errors.submit} />
      )}

      {/* Action Buttons */}
      <div className="flex justify-between pt-6">
        <SecondaryButton onClick={onPrevious}>
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </SecondaryButton>

        <PrimaryButton onClick={handleNext}>
          Fortsett
          <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </PrimaryButton>
      </div>
    </div>
  );
};

export default Step3ContactDetails;
