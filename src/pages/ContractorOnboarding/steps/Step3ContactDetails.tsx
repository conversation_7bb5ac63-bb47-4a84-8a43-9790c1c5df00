import React from 'react';
import { PrimaryButton, SecondaryButton, TextInput, TextArea, FormError } from '../../../components/ui';
import type { ContractorOnboardingFormData } from '../ContractorOnboardingWizard';

interface Step3ContactDetailsProps {
  formData: ContractorOnboardingFormData;
  updateFormData: (updates: Partial<ContractorOnboardingFormData>) => void;
  errors: { [key: string]: string };
  onNext: () => void;
  onPrevious: () => void;
  setErrors: (errors: { [key: string]: string }) => void;
}

/**
 * Step 3: Contact Details
 * 
 * Collects contractor contact information:
 * - Contact person name
 * - Phone number (Norwegian format)
 * - Email address
 * - Optional notes
 */
export const Step3ContactDetails: React.FC<Step3ContactDetailsProps> = ({
  formData,
  updateFormData,
  errors,
  onNext,
  onPrevious,
  setErrors
}) => {
  // Format phone number as user types
  const handlePhoneChange = (value: string) => {
    // Remove all non-digit characters except +
    let cleaned = value.replace(/[^\d+]/g, '');
    
    // If it starts with +47, format as +47 XXX XX XXX
    if (cleaned.startsWith('+47')) {
      const digits = cleaned.substring(3);
      if (digits.length <= 8) {
        const formatted = digits.replace(/(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4').trim();
        cleaned = `+47 ${formatted}`;
      }
    } else if (cleaned.startsWith('47') && cleaned.length > 2) {
      // Convert 47XXXXXXXX to +47 XXX XX XXX
      const digits = cleaned.substring(2);
      if (digits.length <= 8) {
        const formatted = digits.replace(/(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4').trim();
        cleaned = `+47 ${formatted}`;
      }
    } else if (cleaned.length === 8 && !cleaned.startsWith('+')) {
      // Format 8-digit number as +47 XXX XX XXX
      const formatted = cleaned.replace(/(\d{2})(\d{2})(\d{2})(\d{2})/, '$1 $2 $3 $4');
      cleaned = `+47 ${formatted}`;
    }
    
    updateFormData({ phone: cleaned });
    
    // Clear phone error when user types
    if (errors.phone) {
      setErrors({ ...errors, phone: '' });
    }
  };

  // Validate email format as user types
  const handleEmailChange = (value: string) => {
    updateFormData({ email: value });
    
    // Clear email error when user types
    if (errors.email) {
      setErrors({ ...errors, email: '' });
    }
  };

  // Validate contact person name
  const handleContactPersonChange = (value: string) => {
    updateFormData({ contactPerson: value });
    
    // Clear error when user types
    if (errors.contactPerson) {
      setErrors({ ...errors, contactPerson: '' });
    }
  };

  // Validate form and proceed to next step
  const handleNext = () => {
    const newErrors: { [key: string]: string } = {};

    // Validate contact person
    if (!formData.contactPerson.trim()) {
      newErrors.contactPerson = 'Kontaktperson er påkrevd';
    }

    // Validate phone number
    if (!formData.phone.trim()) {
      newErrors.phone = 'Telefonnummer er påkrevd';
    } else {
      // Check Norwegian phone number format
      const phoneRegex = /^(\+47\s?)?\d{2}\s?\d{2}\s?\d{2}\s?\d{2}$/;
      if (!phoneRegex.test(formData.phone.trim())) {
        newErrors.phone = 'Ugyldig telefonnummer format (bruk +47 XXX XX XXX)';
      }
    }

    // Validate email
    if (!formData.email.trim()) {
      newErrors.email = 'E-postadresse er påkrevd';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email.trim())) {
        newErrors.email = 'Ugyldig e-postadresse format';
      }
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Clear errors and proceed
    setErrors({});
    onNext();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-jobblogg-text-strong mb-2">
          Kontaktinformasjon
        </h2>
        <p className="text-jobblogg-text-muted">
          Legg til dine kontaktdetaljer for kommunikasjon med kunder
        </p>
      </div>

      {/* Contact Person */}
      <div className="space-y-2">
        <TextInput
          label="Kontaktperson"
          placeholder="Arne Løken"
          required
          fullWidth
          value={formData.contactPerson}
          onChange={(e) => handleContactPersonChange(e.target.value)}
          error={errors.contactPerson}
          helperText="Navn på person som skal være hovedkontakt for prosjekter"
        />
      </div>

      {/* Phone Number */}
      <div className="space-y-2">
        <TextInput
          label="Telefonnummer"
          placeholder="+47 XXX XX XXX"
          required
          fullWidth
          value={formData.phone}
          onChange={(e) => handlePhoneChange(e.target.value)}
          error={errors.phone}
          helperText="Norsk telefonnummer med landskode"
        />
      </div>

      {/* Email Address */}
      <div className="space-y-2">
        <TextInput
          label="E-postadresse"
          placeholder="<EMAIL>"
          type="email"
          required
          fullWidth
          value={formData.email}
          onChange={(e) => handleEmailChange(e.target.value)}
          error={errors.email}
          helperText="E-postadresse for kommunikasjon og varsler"
        />
      </div>

      {/* Notes (Optional) */}
      <div className="space-y-2">
        <TextArea
          label="Notater (valgfritt)"
          placeholder="Tilleggsinformasjon om bedriften eller spesielle instruksjoner..."
          fullWidth
          rows={3}
          value={formData.notes}
          onChange={(e) => updateFormData({ notes: e.target.value })}
          error={errors.notes}
          helperText="Eventuell tilleggsinformasjon som kan være nyttig for kunder"
        />
      </div>

      {/* Information Box */}
      <div className="bg-jobblogg-primary-soft rounded-xl p-6">
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-1">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h4 className="font-semibold text-jobblogg-text-strong mb-2">
              Hvordan brukes kontaktinformasjonen?
            </h4>
            <div className="space-y-2 text-sm text-jobblogg-text-muted">
              <p>
                • <strong>Kontaktperson:</strong> Vises til kunder i delte prosjekter og chat-meldinger
              </p>
              <p>
                • <strong>Telefon:</strong> Brukes for direkte kontakt og vises i prosjektdetaljer
              </p>
              <p>
                • <strong>E-post:</strong> For varsler, rapporter og kommunikasjon via systemet
              </p>
              <p>
                • <strong>Notater:</strong> Vises kun til kunder i delte prosjekter (valgfritt)
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Form Errors */}
      {errors.submit && (
        <FormError message={errors.submit} />
      )}

      {/* Action Buttons */}
      <div className="flex justify-between pt-6">
        <SecondaryButton onClick={onPrevious}>
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </SecondaryButton>

        <PrimaryButton onClick={handleNext}>
          Fortsett
          <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </PrimaryButton>
      </div>
    </div>
  );
};

export default Step3ContactDetails;
