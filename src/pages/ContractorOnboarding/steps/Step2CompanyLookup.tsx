import React, { useRef, useState } from 'react';
import { PrimaryButton, SecondaryButton, TextInput, FormError, Alert, LockedInput } from '../../../components/ui';
import { CompanyLookup, CompanyLookupRef } from '../../../components/CompanyLookup/CompanyLookup';
import { CompanyInfo } from '../../../services/companyLookup';
import type { ContractorOnboardingFormData } from '../ContractorOnboardingWizard';

interface Step2CompanyLookupProps {
  formData: ContractorOnboardingFormData;
  updateFormData: (updates: Partial<ContractorOnboardingFormData>) => void;
  errors: { [key: string]: string };
  onNext: () => void;
  onPrevious: () => void;
  brregData: any;
  setBrregData: (data: any) => void;
  brregFetchedAt: number | null;
  setBrregFetchedAt: (timestamp: number | null) => void;
  useCustomAddress: boolean;
  setUseCustomAddress: (use: boolean) => void;
  lockedFields: { orgNumber?: boolean; address?: boolean };
  setLockedFields: (fields: { orgNumber?: boolean; address?: boolean }) => void;
  companySelected: boolean;
  setCompanySelected: (selected: boolean) => void;
  setErrors: (errors: { [key: string]: string }) => void;
}

/**
 * Step 2: Company Lookup with Brønnøysundregisteret integration
 * 
 * Features:
 * - Search-as-you-type company lookup
 * - Auto-fill from Brønnøysundregisteret
 * - Manual entry fallback
 * - Field locking for auto-populated data
 * - Organization number validation
 */
export const Step2CompanyLookup: React.FC<Step2CompanyLookupProps> = ({
  formData,
  updateFormData,
  errors,
  onNext,
  onPrevious,
  brregData,
  setBrregData,
  brregFetchedAt,
  setBrregFetchedAt,
  useCustomAddress,
  setUseCustomAddress,
  lockedFields,
  setLockedFields,
  companySelected,
  setCompanySelected,
  setErrors
}) => {
  const companyLookupRef = useRef<CompanyLookupRef>(null);
  const [isValidating, setIsValidating] = useState(false);

  // Handle company selection from Brønnøysundregisteret
  const handleCompanySelect = (company: CompanyInfo) => {
    const brregTimestamp = Date.now();
    setBrregData(company);
    setBrregFetchedAt(brregTimestamp);

    // Auto-fill form fields
    updateFormData({
      companyName: company.name,
      orgNumber: company.organizationNumber,
      streetAddress: company.visitingAddress?.street || company.businessAddress?.street || '',
      postalCode: company.visitingAddress?.postalCode || company.businessAddress?.postalCode || '',
      city: company.visitingAddress?.city || company.businessAddress?.city || ''
    });

    // Lock fields populated from Brønnøysundregisteret
    setLockedFields({
      orgNumber: !!company.organizationNumber,
      address: !!(company.visitingAddress || company.businessAddress)
    });

    // Reset address override
    setUseCustomAddress(false);
    setCompanySelected(true);
    
    // Clear any existing errors
    setErrors({});
  };

  // Handle manual company name change
  const handleCompanyNameChange = (name: string) => {
    updateFormData({ companyName: name });
    
    // If user starts typing manually, reset Brønnøysundregisteret data
    if (companySelected && name !== brregData?.name) {
      setBrregData(null);
      setBrregFetchedAt(null);
      setLockedFields({ orgNumber: false, address: false });
      setCompanySelected(false);
      setUseCustomAddress(false);
    }
  };

  // Handle organization number change
  const handleOrgNumberChange = (orgNumber: string) => {
    updateFormData({ orgNumber });
    
    // Clear validation errors when user types
    if (errors.orgNumber) {
      setErrors({ ...errors, orgNumber: '' });
    }
  };

  // Validate form and proceed to next step (simplified without Convex validation)
  const handleNext = async () => {
    const newErrors: { [key: string]: string } = {};

    // Validate required fields
    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Bedriftsnavn er påkrevd';
    }

    if (!formData.orgNumber.trim()) {
      newErrors.orgNumber = 'Organisasjonsnummer er påkrevd';
    } else {
      // Validate organization number format (client-side only for now)
      const cleanOrgNumber = formData.orgNumber.replace(/\s/g, '');
      if (!/^\d{9}$/.test(cleanOrgNumber)) {
        newErrors.orgNumber = 'Organisasjonsnummer må være 9 siffer';
      }
      // Note: Backend validation will happen during final submission
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Clear errors and proceed
    setErrors({});
    onNext();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-jobblogg-text-strong mb-2">
          Registrer din bedrift
        </h2>
        <p className="text-jobblogg-text-muted">
          Søk etter din bedrift i Brønnøysundregisteret eller legg inn informasjonen manuelt
        </p>
      </div>

      {/* Company Lookup */}
      <div className="space-y-4">
        <CompanyLookup
          ref={companyLookupRef}
          companyName={formData.companyName}
          onCompanyNameChange={handleCompanyNameChange}
          onCompanySelect={handleCompanySelect}
          error={errors.companyName}
        />

        {/* Manual company name input fallback */}
        {!companySelected && (
          <div className="text-sm text-jobblogg-text-muted bg-jobblogg-neutral-secondary rounded-lg p-3">
            <p className="mb-2">
              <strong>Finner du ikke bedriften din?</strong>
            </p>
            <p>
              Du kan skrive inn bedriftsnavnet manuelt og legge til organisasjonsnummeret nedenfor.
            </p>
          </div>
        )}
      </div>

      {/* Organization Number */}
      <div className="space-y-2">
        {lockedFields.orgNumber ? (
          <LockedInput
            label="Organisasjonsnummer"
            value={formData.orgNumber}
            fullWidth
            helperText="Hentet automatisk fra Brønnøysundregisteret"
          />
        ) : (
          <TextInput
            label="Organisasjonsnummer"
            placeholder="***********"
            required
            fullWidth
            value={formData.orgNumber}
            onChange={(e) => handleOrgNumberChange(e.target.value)}
            error={errors.orgNumber}
            helperText="9 siffer uten mellomrom"
          />
        )}
      </div>

      {/* Address Fields */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-jobblogg-text-strong">
          Besøksadresse
        </h3>

        {/* Address Override Toggle */}
        {lockedFields.address && (
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              id="useCustomAddress"
              checked={useCustomAddress}
              onChange={(e) => setUseCustomAddress(e.target.checked)}
              className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border rounded focus:ring-jobblogg-primary focus:ring-2"
            />
            <label htmlFor="useCustomAddress" className="text-sm text-jobblogg-text-muted">
              Bruk annen adresse enn den som er registrert i Brønnøysundregisteret
            </label>
          </div>
        )}

        <div className="grid gap-4 md:grid-cols-2">
          {/* Street Address */}
          {lockedFields.address && !useCustomAddress ? (
            <div className="md:col-span-2">
              <LockedInput
                label="Gateadresse"
                value={formData.streetAddress}
                fullWidth
                helperText="Hentet automatisk fra Brønnøysundregisteret"
              />
            </div>
          ) : (
            <div className="md:col-span-2">
              <TextInput
                label="Gateadresse"
                placeholder="Storgata 15"
                fullWidth
                value={formData.streetAddress}
                onChange={(e) => updateFormData({ streetAddress: e.target.value })}
                error={errors.streetAddress}
              />
            </div>
          )}

          {/* Postal Code */}
          {lockedFields.address && !useCustomAddress ? (
            <LockedInput
              label="Postnummer"
              value={formData.postalCode}
              fullWidth
              helperText="Auto"
            />
          ) : (
            <TextInput
              label="Postnummer"
              placeholder="0123"
              fullWidth
              value={formData.postalCode}
              onChange={(e) => updateFormData({ postalCode: e.target.value })}
              error={errors.postalCode}
            />
          )}

          {/* City */}
          {lockedFields.address && !useCustomAddress ? (
            <LockedInput
              label="Poststed"
              value={formData.city}
              fullWidth
              helperText="Auto"
            />
          ) : (
            <TextInput
              label="Poststed"
              placeholder="Oslo"
              fullWidth
              value={formData.city}
              onChange={(e) => updateFormData({ city: e.target.value })}
              error={errors.city}
            />
          )}
        </div>

        {/* Entrance */}
        <TextInput
          label="Inngang/Etasje (valgfritt)"
          placeholder="Oppgang A, 2. etasje"
          fullWidth
          value={formData.entrance}
          onChange={(e) => updateFormData({ entrance: e.target.value })}
          error={errors.entrance}
          helperText="Tilleggsinformasjon for å finne bedriften"
        />
      </div>

      {/* Success Message */}
      {companySelected && brregData && (
        <Alert
          type="success"
          title="Bedrift funnet!"
          message={`Informasjon hentet fra Brønnøysundregisteret for ${brregData.name}`}
        />
      )}

      {/* Form Errors */}
      {errors.submit && (
        <FormError message={errors.submit} />
      )}

      {/* Action Buttons */}
      <div className="flex justify-between pt-6">
        <SecondaryButton onClick={onPrevious}>
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Tilbake
        </SecondaryButton>

        <PrimaryButton 
          onClick={handleNext}
          disabled={isValidating}
        >
          {isValidating ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Validerer...
            </>
          ) : (
            <>
              Fortsett
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </>
          )}
        </PrimaryButton>
      </div>
    </div>
  );
};

export default Step2CompanyLookup;
