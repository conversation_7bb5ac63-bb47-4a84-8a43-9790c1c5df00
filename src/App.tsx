import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { SignedIn, SignedOut } from '@clerk/clerk-react'
import { PWAInstallBanner } from './components/PWAInstallBanner'
import { OfflineSync, StorageUsage } from './components/OfflineSync'
import { ContractorOnboardingGuard } from './components/ContractorOnboardingGuard'
import {
  LazyDashboard,
  LazyCreateProject,
  LazyProjectLog,
  LazyProjectDetail,
  LazySignIn,
  LazySignUp,
  LazySharedProject,
  LazyConversations,
  LazyArchivedProjects,
  LazyGoogleMapsTest,
  LazyContractorOnboarding,
  LazyPageWrapper,
  preloadCriticalRoutes
} from './components/LazyComponents'
import { useEffect } from 'react'

function App() {
  // Preload critical routes on app initialization
  useEffect(() => {
    preloadCriticalRoutes();
  }, []);

  return (
    <BrowserRouter>
      <div className="min-h-screen bg-white transition-colors duration-300">
        {/* PWA Components */}
        <PWAInstallBanner />
        <OfflineSync />
        <StorageUsage />
        <Routes>
          {/* Public Routes - Accessible to everyone */}
          <Route path="/shared/:sharedId" element={
            <LazyPageWrapper>
              <LazySharedProject />
            </LazyPageWrapper>
          } />
          {/* Remove chat route - now using embedded chat */}

          {/* Authentication Routes - Accessible to unauthenticated users */}
          <Route path="/sign-in" element={
            <LazyPageWrapper>
              <LazySignIn />
            </LazyPageWrapper>
          } />
          <Route path="/sign-up" element={
            <LazyPageWrapper>
              <LazySignUp />
            </LazyPageWrapper>
          } />

          {/* Contractor Onboarding - Requires authentication but bypasses onboarding guard */}
          <Route path="/contractor-onboarding/*" element={
            <>
              <SignedIn>
                <LazyPageWrapper>
                  <LazyContractorOnboarding />
                </LazyPageWrapper>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />

          {/* Protected Routes - Require authentication and completed contractor onboarding */}
          <Route path="/" element={
            <>
              <SignedIn>
                <ContractorOnboardingGuard>
                  <LazyPageWrapper>
                    <LazyDashboard />
                  </LazyPageWrapper>
                </ContractorOnboardingGuard>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />

          <Route path="/archived-projects" element={
            <>
              <SignedIn>
                <ContractorOnboardingGuard>
                  <LazyPageWrapper>
                    <LazyArchivedProjects />
                  </LazyPageWrapper>
                </ContractorOnboardingGuard>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/conversations" element={
            <>
              <SignedIn>
                <ContractorOnboardingGuard>
                  <LazyPageWrapper>
                    <LazyConversations />
                  </LazyPageWrapper>
                </ContractorOnboardingGuard>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/create" element={
            <>
              <SignedIn>
                <ContractorOnboardingGuard>
                  <LazyPageWrapper>
                    <LazyCreateProject />
                  </LazyPageWrapper>
                </ContractorOnboardingGuard>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/create-wizard" element={
            <>
              <SignedIn>
                <ContractorOnboardingGuard>
                  <LazyPageWrapper>
                    <LazyCreateProject />
                  </LazyPageWrapper>
                </ContractorOnboardingGuard>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/test-google-maps" element={
            <>
              <SignedIn>
                <ContractorOnboardingGuard>
                  <LazyPageWrapper>
                    <LazyGoogleMapsTest />
                  </LazyPageWrapper>
                </ContractorOnboardingGuard>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />

          <Route path="/project/:projectId/details" element={
            <>
              <SignedIn>
                <ContractorOnboardingGuard>
                  <LazyPageWrapper>
                    <LazyProjectDetail />
                  </LazyPageWrapper>
                </ContractorOnboardingGuard>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          <Route path="/project/:projectId" element={
            <>
              <SignedIn>
                <ContractorOnboardingGuard>
                  <LazyPageWrapper>
                    <LazyProjectLog />
                  </LazyPageWrapper>
                </ContractorOnboardingGuard>
              </SignedIn>
              <SignedOut>
                <Navigate to="/sign-in" replace />
              </SignedOut>
            </>
          } />
          {/* Remove chat route - now using embedded chat */}
        </Routes>
      </div>
    </BrowserRouter>
  )
}

export default App
