import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser, useAuth } from '@clerk/clerk-react';
import { api } from '../../convex/_generated/api';

interface ContractorOnboardingGuardProps {
  children: React.ReactNode;
}

/**
 * Route protection component that ensures contractors complete onboarding
 * before accessing the main application.
 * 
 * Flow:
 * 1. Check if user is authenticated (Clerk)
 * 2. Check contractor onboarding status from Convex
 * 3. Redirect to onboarding if incomplete
 * 4. Allow access to main app if complete
 */
export const ContractorOnboardingGuard: React.FC<ContractorOnboardingGuardProps> = ({ children }) => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();
  const location = useLocation();
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const [hasTriedCreatingUser, setHasTriedCreatingUser] = useState(false);

  // Mutation to create user if it doesn't exist
  const getOrCreateUser = useMutation(api.contractorOnboarding.getOrCreateUser);

  // Only query contractor onboarding status when both Clerk and Convex auth are ready
  const shouldQueryOnboarding = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id;

  const onboardingStatus = useQuery(
    api.contractorOnboarding.getContractorOnboardingStatus,
    shouldQueryOnboarding ? { clerkUserId: user.id } : "skip"
  );

  // Handle loading states and onboarding check with retry mechanism and user creation
  useEffect(() => {
    if (isClerkLoaded && isAuthLoaded) {
      if (!isSignedIn || !user) {
        setIsCheckingOnboarding(false);
      } else if (onboardingStatus !== undefined) {
        setIsCheckingOnboarding(false);
        setRetryCount(0); // Reset retry count on success
      } else if (shouldQueryOnboarding && retryCount < 2) {
        // If we should be able to query but haven't gotten a result, retry after a delay
        const timer = setTimeout(() => {
          setRetryCount(prev => prev + 1);
        }, 1000 * (retryCount + 1)); // Exponential backoff: 1s, 2s

        return () => clearTimeout(timer);
      } else if (retryCount >= 2 && !hasTriedCreatingUser && user?.id) {
        // After 2 retries, try to create the user record
        console.log("Attempting to create user record for", user.id);
        setHasTriedCreatingUser(true);
        getOrCreateUser({ clerkUserId: user.id })
          .then(() => {
            console.log("User record created/retrieved successfully");
            setRetryCount(0);
          })
          .catch((error) => {
            console.error("Failed to create user record:", error);
            setIsCheckingOnboarding(false);
          });
      } else if (hasTriedCreatingUser && retryCount >= 2) {
        // After trying to create user and still no result, assume not completed
        console.warn("Failed to get onboarding status after user creation, assuming not completed");
        setIsCheckingOnboarding(false);
      }
    }
  }, [isClerkLoaded, isAuthLoaded, isSignedIn, user, onboardingStatus, shouldQueryOnboarding, retryCount, hasTriedCreatingUser, getOrCreateUser]);

  // Show loading state while checking authentication and onboarding
  if (!isClerkLoaded || !isAuthLoaded || isCheckingOnboarding) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-jobblogg-text-strong">
              Laster JobbLogg...
            </h2>
            <p className="text-jobblogg-text-muted">
              {!isClerkLoaded ? 'Starter autentisering...' :
               !isAuthLoaded ? 'Synkroniserer med server...' :
               hasTriedCreatingUser ? 'Oppretter brukeroppføring...' :
               retryCount > 0 ? `Prøver igjen (${retryCount}/2)...` :
               'Sjekker brukerinformasjon...'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If user is not authenticated, let Clerk handle the redirect
  if (!isSignedIn || !user) {
    return <Navigate to="/sign-in" replace />;
  }

  // If onboarding status is not loaded yet and we haven't exhausted retries, show loading
  if (onboardingStatus === undefined && (retryCount < 2 || hasTriedCreatingUser)) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-jobblogg-text-strong">
              Sjekker bedriftsinformasjon...
            </h2>
            <p className="text-jobblogg-text-muted">
              {hasTriedCreatingUser ? 'Oppretter brukeroppføring...' :
               retryCount > 0 ? `Prøver igjen (${retryCount}/2)...` :
               'Vent litt mens vi henter dine data'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If contractor onboarding is not completed (or we couldn't determine status), redirect to onboarding
  if (!onboardingStatus?.contractorCompleted) {
    // Preserve the intended destination for redirect after onboarding
    const redirectTo = location.pathname !== '/contractor-onboarding' ? location.pathname : '/';
    const onboardingUrl = `/contractor-onboarding${redirectTo !== '/' ? `?redirect=${encodeURIComponent(redirectTo)}` : ''}`;

    return <Navigate to={onboardingUrl} replace />;
  }

  // Contractor onboarding is complete, render the protected content
  return <>{children}</>;
};

/**
 * Hook to get contractor onboarding status
 * Useful for components that need to conditionally render based on onboarding status
 */
export const useContractorOnboardingStatus = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();

  // Only query when both Clerk and Convex auth are ready
  const shouldQuery = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id;

  const onboardingStatus = useQuery(
    api.contractorOnboarding.getContractorOnboardingStatus,
    shouldQuery ? { clerkUserId: user.id } : "skip"
  );

  return {
    isLoading: !shouldQuery || onboardingStatus === undefined,
    exists: onboardingStatus?.exists || false,
    contractorCompleted: onboardingStatus?.contractorCompleted || false,
    contractorCompanyId: onboardingStatus?.contractorCompanyId || null,
  };
};

/**
 * Hook to get contractor company information
 * Returns the contractor's company data if available
 */
export const useContractorCompany = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();

  // Only query when both Clerk and Convex auth are ready
  const shouldQuery = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id;

  const contractorCompany = useQuery(
    api.contractorOnboarding.getContractorCompanyByUserId,
    shouldQuery ? { clerkUserId: user.id } : "skip"
  );

  return {
    isLoading: !shouldQuery || contractorCompany === undefined,
    company: contractorCompany || null,
  };
};
