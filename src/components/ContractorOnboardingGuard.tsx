import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../convex/_generated/api';

interface ContractorOnboardingGuardProps {
  children: React.ReactNode;
}

/**
 * Route protection component that ensures contractors complete onboarding
 * before accessing the main application.
 * 
 * Flow:
 * 1. Check if user is authenticated (Clerk)
 * 2. Check contractor onboarding status from Convex
 * 3. Redirect to onboarding if incomplete
 * 4. Allow access to main app if complete
 */
export const ContractorOnboardingGuard: React.FC<ContractorOnboardingGuardProps> = ({ children }) => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const location = useLocation();
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(true);

  // Query contractor onboarding status
  const onboardingStatus = useQuery(
    api.contractorOnboarding.getContractorOnboardingStatus,
    user?.id ? { clerkUserId: user.id } : "skip"
  );

  // Handle loading states and onboarding check
  useEffect(() => {
    if (isClerkLoaded && user && onboardingStatus !== undefined) {
      setIsCheckingOnboarding(false);
    }
  }, [isClerkLoaded, user, onboardingStatus]);

  // Show loading state while checking authentication and onboarding
  if (!isClerkLoaded || isCheckingOnboarding) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-jobblogg-text-strong">
              Laster JobbLogg...
            </h2>
            <p className="text-jobblogg-text-muted">
              Sjekker brukerinformasjon
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If user is not authenticated, let Clerk handle the redirect
  if (!user) {
    return <Navigate to="/sign-in" replace />;
  }

  // If onboarding status is not loaded yet, show loading
  if (onboardingStatus === undefined) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-jobblogg-text-strong">
              Sjekker bedriftsinformasjon...
            </h2>
            <p className="text-jobblogg-text-muted">
              Vent litt mens vi henter dine data
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If contractor onboarding is not completed, redirect to onboarding
  if (!onboardingStatus.contractorCompleted) {
    // Preserve the intended destination for redirect after onboarding
    const redirectTo = location.pathname !== '/contractor-onboarding' ? location.pathname : '/';
    const onboardingUrl = `/contractor-onboarding${redirectTo !== '/' ? `?redirect=${encodeURIComponent(redirectTo)}` : ''}`;
    
    return <Navigate to={onboardingUrl} replace />;
  }

  // Contractor onboarding is complete, render the protected content
  return <>{children}</>;
};

/**
 * Hook to get contractor onboarding status
 * Useful for components that need to conditionally render based on onboarding status
 */
export const useContractorOnboardingStatus = () => {
  const { user } = useUser();
  
  const onboardingStatus = useQuery(
    api.contractorOnboarding.getContractorOnboardingStatus,
    user?.id ? { clerkUserId: user.id } : "skip"
  );

  return {
    isLoading: onboardingStatus === undefined,
    exists: onboardingStatus?.exists || false,
    contractorCompleted: onboardingStatus?.contractorCompleted || false,
    contractorCompanyId: onboardingStatus?.contractorCompanyId || null,
  };
};

/**
 * Hook to get contractor company information
 * Returns the contractor's company data if available
 */
export const useContractorCompany = () => {
  const { user } = useUser();
  
  const contractorCompany = useQuery(
    api.contractorOnboarding.getContractorCompanyByUserId,
    user?.id ? { clerkUserId: user.id } : "skip"
  );

  return {
    isLoading: contractorCompany === undefined,
    company: contractorCompany || null,
  };
};
